namespace Curio.Shared.Users;

// 用户相关的数据传输对象
public class UserDto
{
    public string Id { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public DateTime RegisteredAt { get; set; }
    public bool IsVerified { get; set; }
}

// 命令对象
public class CheckEmailExistsCommand
{
    public string Email { get; set; } = string.Empty;
    public string CommandId { get; set; } = Guid.NewGuid().ToString();
}

public class SendVerificationCodeCommand
{
    public string Email { get; set; } = string.Empty;
    public string Purpose { get; set; } = "registration"; // "registration" or "login"
    public string CommandId { get; set; } = Guid.NewGuid().ToString();
}

public class RegisterUserCommand
{
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string VerificationCode { get; set; } = string.Empty;
    public string CommandId { get; set; } = Guid.NewGuid().ToString();
}

public class LoginUserCommand
{
    public string Email { get; set; } = string.Empty;
    public string VerificationCode { get; set; } = string.Empty;
    public string CommandId { get; set; } = Guid.NewGuid().ToString();
}

// 查询结果
public class EmailExistsResult
{
    public bool Exists { get; set; }
    public bool IsVerified { get; set; }
}

public class VerificationResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public UserDto? User { get; set; }
}