namespace Curio.Shared.Users;

// 基础领域事件
public abstract class DomainEvent
{
    public string EventId { get; set; } = Guid.NewGuid().ToString();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public string CommandId { get; set; } = string.Empty;
    public string IdempotencyKey { get; set; } = string.Empty;
}

// 用户相关领域事件
public class UserRegistrationInitiatedEvent : DomainEvent
{
    public string Email { get; set; } = string.Empty;
    public string VerificationCode { get; set; } = string.Empty;
    public DateTime ExpiresAt { get; set; }
}

public class UserRegisteredEvent : DomainEvent
{
    public string UserId { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public DateTime RegisteredAt { get; set; }
}

public class UserLoginAttemptedEvent : DomainEvent
{
    public string Email { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? FailureReason { get; set; }
    public DateTime AttemptedAt { get; set; }
}

public class VerificationCodeGeneratedEvent : DomainEvent
{
    public string Email { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public DateTime ExpiresAt { get; set; }
    public string Purpose { get; set; } = string.Empty; // "registration" or "login"
}