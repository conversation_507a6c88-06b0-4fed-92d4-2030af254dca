using Orleans;
using Curio.Application.Interfaces;
using Curio.Orleans.Interfaces.Users;
using Curio.Shared.Users;

namespace Curio.Application.Implementation;

public class UserService : IUserService
{
    private readonly IGrainFactory _grainFactory;
    private readonly IEmailService _emailService;

    public UserService(IGrainFactory grainFactory, IEmailService emailService)
    {
        _grainFactory = grainFactory;
        _emailService = emailService;
    }

    public async Task<EmailExistsResult> CheckEmailExistsAsync(CheckEmailExistsCommand command)
    {
        var userGrain = _grainFactory.GetGrain<IUserGrain>(command.Email);
        return await userGrain.CheckEmailExistsAsync(command);
    }

    public async Task<bool> SendVerificationCodeAsync(SendVerificationCodeCommand command)
    {
        var verificationGrain = _grainFactory.GetGrain<IVerificationGrain>(command.Email);
        var codeGenerated = await verificationGrain.SendVerificationCodeAsync(command);

        if (codeGenerated)
        {
            // Get the generated code to send via email
            var code = await verificationGrain.GetValidCodeAsync(command.Purpose);
            if (code != null)
            {
                await _emailService.SendVerificationCodeAsync(command.Email, code, command.Purpose);
            }
        }

        return codeGenerated;
    }

    public async Task<VerificationResult> RegisterUserAsync(RegisterUserCommand command)
    {
        var userGrain = _grainFactory.GetGrain<IUserGrain>(command.Email);
        return await userGrain.RegisterUserAsync(command);
    }

    public async Task<VerificationResult> LoginUserAsync(LoginUserCommand command)
    {
        var userGrain = _grainFactory.GetGrain<IUserGrain>(command.Email);
        return await userGrain.LoginUserAsync(command);
    }

    public async Task<UserDto?> GetUserAsync(string email)
    {
        var userGrain = _grainFactory.GetGrain<IUserGrain>(email);
        return await userGrain.GetUserAsync();
    }
}