{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Orleans": "Information", "Microsoft.Orleans": "Warning", "Curio.Infrastructure.Services": "Debug"}}, "Application": {"Name": "Curio Orleans Silo", "Version": "1.0.0", "Environment": "Development"}, "Database": {"ConnectionString": "Host=localhost;Port=5432;Database=curio;Username=orleans;Password=**********", "Host": "localhost", "Port": 5432, "Database": "curio", "Username": "orleans", "Password": "**********", "CommandTimeout": 30, "MaxRetryCount": 3, "EnableSensitiveDataLogging": true}, "OrleansConfig": {"ClusterId": "curio-cluster-dev", "ServiceId": "curio-service-dev", "Clustering": {"Provider": "AdoNet", "ConnectionString": "Host=localhost;Port=5432;Database=curio;Username=orleans;Password=**********", "RefreshPeriod": 30, "DeathVoteExpirationTimeout": 120}, "Storage": {"DefaultProvider": "AdoNet", "ConnectionString": "Host=localhost;Port=5432;Database=curio;Username=orleans;Password=**********", "UseJsonFormat": true}, "Streaming": {"Provider": "Kafka", "ConnectionString": "localhost:9092"}, "Reminders": {"Provider": "AdoNet", "ConnectionString": "Host=localhost;Port=5432;Database=curio;Username=orleans;Password=**********"}}, "Kafka": {"BrokerList": ["localhost:9092"], "ConsumerGroupId": "orleans-event-streams-dev", "Topics": ["domain-events", "verification-events", "user-events"], "SessionTimeoutMs": 30000, "HeartbeatIntervalMs": 3000, "AutoOffsetReset": "earliest", "EnableAutoCommit": true, "AutoCommitIntervalMs": 5000, "MaxPollRecords": 500, "FetchMinBytes": 1, "FetchMaxWaitMs": 500, "SecurityProtocol": "SASL_PLAINTEXT", "SaslMechanism": "PLAIN", "SaslUsername": "admin", "SaslPassword": "admin-secret"}, "Email": {"Smtp": {"Host": "smtp.gmail.com", "Port": 587, "Username": "<EMAIL>", "Password": "your-app-password", "EnableSsl": true, "TimeoutSeconds": 30, "UseDefaultCredentials": false}, "Templates": {"TemplatesDirectory": "EmailTemplates", "EnableCaching": true, "CacheExpirationMinutes": 5}, "DefaultSender": {"FromEmail": "<EMAIL>", "FromName": "Curio Development", "ReplyToEmail": "<EMAIL>", "ReplyToName": "Curio Support"}, "Retry": {"MaxAttempts": 3, "DelayMilliseconds": 1000, "ExponentialBackoff": true}}}