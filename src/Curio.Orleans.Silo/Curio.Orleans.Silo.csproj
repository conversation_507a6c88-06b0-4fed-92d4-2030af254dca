<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\Curio.Shared\Curio.Shared.csproj" />
    <ProjectReference Include="..\Curio.Orleans.Interfaces\Curio.Orleans.Interfaces.csproj" />
    <ProjectReference Include="..\Curio.Orleans.Grains\Curio.Orleans.Grains.csproj" />
    <ProjectReference Include="..\Curio.Infrastructure\Curio.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Orleans.Sdk" Version="9.2.1" />
    <PackageReference Include="Microsoft.Orleans.Clustering.AdoNet" Version="9.2.1" />
    <PackageReference Include="Microsoft.Orleans.Persistence.AdoNet" Version="9.2.1" />
    <PackageReference Include="Microsoft.Orleans.Reminders.AdoNet" Version="9.2.1" />
    <PackageReference Include="Microsoft.Orleans.Server" Version="9.2.1" />
    <PackageReference Include="Orleans.Streams.Kafka" Version="8.0.2" />
    <PackageReference Include="Npgsql" Version="9.0.3" />
  </ItemGroup>

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <ServerGarbageCollection>true</ServerGarbageCollection>
  </PropertyGroup>

</Project>
