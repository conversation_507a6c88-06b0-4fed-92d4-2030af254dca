{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "Orleans": "Warning", "Microsoft.Orleans": "Error", "Curio.Infrastructure.Services": "Information"}}, "Application": {"Name": "Curio Orleans Silo", "Version": "1.0.0", "Environment": "Production"}, "Database": {"ConnectionString": "", "Host": "localhost", "Port": 5432, "Database": "orleansdb", "Username": "orleans", "Password": "", "CommandTimeout": 30, "MaxRetryCount": 3, "EnableSensitiveDataLogging": false}, "OrleansConfig": {"ClusterId": "curio-cluster", "ServiceId": "curio-service", "Clustering": {"Provider": "AdoNet", "ConnectionString": "", "RefreshPeriod": 30, "DeathVoteExpirationTimeout": 120}, "Storage": {"DefaultProvider": "AdoNet", "ConnectionString": "", "UseJsonFormat": true}, "Streaming": {"Provider": "Kafka", "ConnectionString": ""}, "Reminders": {"Provider": "AdoNet", "ConnectionString": ""}}, "Kafka": {"BrokerList": ["kafka-prod-1:9092", "kafka-prod-2:9092", "kafka-prod-3:9092"], "ConsumerGroupId": "orleans-event-streams-prod", "Topics": ["domain-events", "verification-events", "user-events"], "SessionTimeoutMs": 30000, "HeartbeatIntervalMs": 3000, "AutoOffsetReset": "earliest", "EnableAutoCommit": true, "AutoCommitIntervalMs": 5000, "MaxPollRecords": 500, "FetchMinBytes": 1, "FetchMaxWaitMs": 500, "SecurityProtocol": "SASL_PLAINTEXT", "SaslMechanism": "PLAIN", "SaslUsername": "", "SaslPassword": ""}, "Email": {"Smtp": {"Host": "", "Port": 587, "Username": "", "Password": "", "EnableSsl": true, "TimeoutSeconds": 30, "UseDefaultCredentials": false}, "Templates": {"TemplatesDirectory": "EmailTemplates", "EnableCaching": true, "CacheExpirationMinutes": 60}, "DefaultSender": {"FromEmail": "", "FromName": "<PERSON><PERSON><PERSON>", "ReplyToEmail": "", "ReplyToName": "Curio Support"}, "Retry": {"MaxAttempts": 3, "DelayMilliseconds": 1000, "ExponentialBackoff": true}}}