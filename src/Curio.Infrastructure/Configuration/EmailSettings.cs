namespace Curio.Infrastructure.Configuration;

// Database Configuration
public class DatabaseSettings
{
    public const string SectionName = "Database";

    public string ConnectionString { get; set; } = string.Empty;
    public string Host { get; set; } = "localhost";
    public int Port { get; set; } = 5432;
    public string Database { get; set; } = "orleansdb";
    public string Username { get; set; } = "orleans";
    public string Password { get; set; } = "orleans123";
    public int CommandTimeout { get; set; } = 30;
    public int MaxRetryCount { get; set; } = 3;
    public bool EnableSensitiveDataLogging { get; set; } = false;
}

// Kafka Configuration
public class KafkaSettings
{
    public const string SectionName = "Kafka";

    public string[] BrokerList { get; set; } = new[] { "localhost:9092" };
    public string ConsumerGroupId { get; set; } = "orleans-event-streams";
    public string[] Topics { get; set; } = new[] { "domain-events", "verification-events", "user-events" };
    public int SessionTimeoutMs { get; set; } = 30000;
    public int HeartbeatIntervalMs { get; set; } = 3000;
    public string AutoOffsetReset { get; set; } = "earliest";
    public bool EnableAutoCommit { get; set; } = true;
    public int AutoCommitIntervalMs { get; set; } = 5000;
    public int MaxPollRecords { get; set; } = 500;
    public int FetchMinBytes { get; set; } = 1;
    public int FetchMaxWaitMs { get; set; } = 500;

    // Security Configuration
    public string SecurityProtocol { get; set; } = "SASL_PLAINTEXT"; // PLAINTEXT, SASL_PLAINTEXT
    public string SaslMechanism { get; set; } = "PLAIN"; // PLAIN认证机制
    public string SaslUsername { get; set; } = string.Empty;
    public string SaslPassword { get; set; } = string.Empty;
}

// Orleans Configuration
public class OrleansSettings
{
    public const string SectionName = "Orleans";

    public string ClusterId { get; set; } = "curio-cluster";
    public string ServiceId { get; set; } = "curio-service";
    public ClusteringSettings Clustering { get; set; } = new();
    public StorageSettings Storage { get; set; } = new();
    public StreamingSettings Streaming { get; set; } = new();
    public ReminderSettings Reminders { get; set; } = new();
}

public class ClusteringSettings
{
    public string Provider { get; set; } = "AdoNet"; // AdoNet, Consul, Memory
    public string ConnectionString { get; set; } = string.Empty;
    public int RefreshPeriod { get; set; } = 30; // seconds
    public int DeathVoteExpirationTimeout { get; set; } = 120; // seconds
}

public class StorageSettings
{
    public string DefaultProvider { get; set; } = "AdoNet";
    public string ConnectionString { get; set; } = string.Empty;
    public bool UseJsonFormat { get; set; } = true;
}

public class StreamingSettings
{
    public string Provider { get; set; } = "Kafka";
    public string ConnectionString { get; set; } = string.Empty;
}

public class ReminderSettings
{
    public string Provider { get; set; } = "AdoNet";
    public string ConnectionString { get; set; } = string.Empty;
}

// Application Configuration
public class ApplicationSettings
{
    public const string SectionName = "Application";

    public string Name { get; set; } = "Curio API";
    public string Version { get; set; } = "1.0.0";
    public string Environment { get; set; } = "Development";
    public ApiSettings Api { get; set; } = new();
    public SecuritySettings Security { get; set; } = new();
}

public class ApiSettings
{
    public string BaseUrl { get; set; } = "https://localhost:7274";
    public string[] AllowedHosts { get; set; } = new[] { "*" };
    public int RequestTimeoutSeconds { get; set; } = 30;
    public int MaxRequestBodySize { get; set; } = 10485760; // 10MB
    public CorsSettings Cors { get; set; } = new();
}

public class CorsSettings
{
    public bool Enabled { get; set; } = true;
    public string[] AllowedOrigins { get; set; } = new[] { "*" };
    public string[] AllowedMethods { get; set; } = new[] { "GET", "POST", "PUT", "DELETE", "OPTIONS" };
    public string[] AllowedHeaders { get; set; } = new[] { "*" };
    public bool AllowCredentials { get; set; } = true;
}

public class SecuritySettings
{
    public JwtSettings Jwt { get; set; } = new();
    public EncryptionSettings Encryption { get; set; } = new();
}

public class JwtSettings
{
    public string SecretKey { get; set; } = string.Empty;
    public string Issuer { get; set; } = "Curio";
    public string Audience { get; set; } = "Curio.Api";
    public int ExpirationMinutes { get; set; } = 60;
    public int RefreshTokenExpirationDays { get; set; } = 7;
}

public class EncryptionSettings
{
    public string Key { get; set; } = string.Empty;
    public string Salt { get; set; } = string.Empty;
}

// Email Configuration (existing)
public class EmailSettings
{
    public const string SectionName = "Email";

    public SmtpSettings Smtp { get; set; } = new();
    public TemplateSettings Templates { get; set; } = new();
    public SenderSettings DefaultSender { get; set; } = new();
    public RetrySettings Retry { get; set; } = new();
}

public class SmtpSettings
{
    public string Host { get; set; } = string.Empty;
    public int Port { get; set; } = 587;
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public bool EnableSsl { get; set; } = true;
    public int TimeoutSeconds { get; set; } = 30;
    public bool UseDefaultCredentials { get; set; } = false;
}

public class TemplateSettings
{
    public string TemplatesDirectory { get; set; } = "EmailTemplates";
    public bool EnableCaching { get; set; } = true;
    public int CacheExpirationMinutes { get; set; } = 60;
}

public class SenderSettings
{
    public string FromEmail { get; set; } = string.Empty;
    public string FromName { get; set; } = string.Empty;
    public string ReplyToEmail { get; set; } = string.Empty;
    public string ReplyToName { get; set; } = string.Empty;
}

public class RetrySettings
{
    public int MaxAttempts { get; set; } = 3;
    public int DelayMilliseconds { get; set; } = 1000;
    public bool ExponentialBackoff { get; set; } = true;
}