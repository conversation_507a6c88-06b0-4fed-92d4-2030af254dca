using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Curio.Application.Interfaces;
using Curio.Infrastructure.Services;
using Curio.Infrastructure.Configuration;

namespace Curio.Infrastructure;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Configure all settings
        services.Configure<DatabaseSettings>(configuration.GetSection(DatabaseSettings.SectionName));
        services.Configure<KafkaSettings>(configuration.GetSection(KafkaSettings.SectionName));
        services.Configure<OrleansSettings>(configuration.GetSection(OrleansSettings.SectionName));
        services.Configure<ApplicationSettings>(configuration.GetSection(ApplicationSettings.SectionName));
        services.Configure<EmailSettings>(configuration.GetSection(EmailSettings.SectionName));

        // Register email services
        services.AddSingleton<IEmailTemplateService, HandlebarsEmailTemplateService>();
        services.AddScoped<ISmtpEmailService, SmtpEmailService>();

        // For backward compatibility, register the old IEmailService interface
        services.AddScoped<IEmailService>(provider =>
        {
            var smtpService = provider.GetRequiredService<ISmtpEmailService>();
            return new EmailServiceAdapter(smtpService);
        });

        // Add memory cache for template caching
        services.AddMemoryCache();

        return services;
    }

    /// <summary>
    /// Get database connection string from configuration
    /// </summary>
    public static string GetDatabaseConnectionString(this IConfiguration configuration)
    {
        var databaseSettings = configuration.GetSection(DatabaseSettings.SectionName).Get<DatabaseSettings>();

        if (!string.IsNullOrEmpty(databaseSettings?.ConnectionString))
        {
            return databaseSettings.ConnectionString;
        }

        // Build connection string from individual components
        return $"Host={databaseSettings?.Host ?? "localhost"};" +
               $"Port={databaseSettings?.Port ?? 5432};" +
               $"Database={databaseSettings?.Database ?? "orleansdb"};" +
               $"Username={databaseSettings?.Username ?? "orleans"};" +
               $"Password={databaseSettings?.Password ?? ""}";
    }

    /// <summary>
    /// Get Orleans connection string from configuration
    /// </summary>
    public static string GetOrleansConnectionString(this IConfiguration configuration, string connectionType = "clustering")
    {
        var orleansSettings = configuration.GetSection(OrleansSettings.SectionName).Get<OrleansSettings>();

        return connectionType.ToLower() switch
        {
            "clustering" => !string.IsNullOrEmpty(orleansSettings?.Clustering?.ConnectionString)
                ? orleansSettings.Clustering.ConnectionString
                : configuration.GetDatabaseConnectionString(),
            "storage" => !string.IsNullOrEmpty(orleansSettings?.Storage?.ConnectionString)
                ? orleansSettings.Storage.ConnectionString
                : configuration.GetDatabaseConnectionString(),
            "reminders" => !string.IsNullOrEmpty(orleansSettings?.Reminders?.ConnectionString)
                ? orleansSettings.Reminders.ConnectionString
                : configuration.GetDatabaseConnectionString(),
            _ => configuration.GetDatabaseConnectionString()
        };
    }

    /// <summary>
    /// Get Kafka brokers from configuration
    /// </summary>
    public static string[] GetKafkaBrokers(this IConfiguration configuration)
    {
        var kafkaSettings = configuration.GetSection(KafkaSettings.SectionName).Get<KafkaSettings>();
        return kafkaSettings?.BrokerList ?? new[] { "localhost:9092" };
    }

    /// <summary>
    /// Get Kafka broker list as string
    /// </summary>
    public static string GetKafkaBrokersString(this IConfiguration configuration)
    {
        return string.Join(",", configuration.GetKafkaBrokers());
    }
}