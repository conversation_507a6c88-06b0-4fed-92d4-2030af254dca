﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\Curio.Shared\Curio.Shared.csproj" />
    <ProjectReference Include="..\Curio.Domain\Curio.Domain.csproj" />
    <ProjectReference Include="..\Curio.Application\Curio.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Handlebars.Net" Version="2.1.6" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.0" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
