{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Curio.Infrastructure.Services": "Debug", "Orleans": "Information", "Microsoft.Orleans": "Warning"}}, "Application": {"Name": "Curio API", "Version": "1.0.0", "Environment": "Development", "Api": {"BaseUrl": "https://localhost:7274", "AllowedHosts": ["*"], "RequestTimeoutSeconds": 30, "MaxRequestBodySize": 10485760, "Cors": {"Enabled": true, "AllowedOrigins": ["http://localhost:3000", "http://localhost:5173", "https://localhost:7274"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["*"], "AllowCredentials": true}}, "Security": {"Jwt": {"SecretKey": "development-secret-key-change-in-production-minimum-32-characters", "Issuer": "Curio.Development", "Audience": "Curio.Api.Development", "ExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}, "Encryption": {"Key": "development-encryption-key-32-chars", "Salt": "development-salt-16-chars"}}}, "Database": {"ConnectionString": "Host=localhost;Port=5432;Database=curio;Username=orleans;Password=**********", "Host": "localhost", "Port": 5432, "Database": "curio", "Username": "orleans", "Password": "**********", "CommandTimeout": 30, "MaxRetryCount": 3, "EnableSensitiveDataLogging": true}, "Orleans": {"ClusterId": "curio-cluster-dev", "ServiceId": "curio-service-dev", "Clustering": {"Provider": "AdoNet", "ConnectionString": "Host=localhost;Port=5432;Database=curio;Username=orleans;Password=**********", "RefreshPeriod": 30, "DeathVoteExpirationTimeout": 120}, "Storage": {"DefaultProvider": "AdoNet", "ConnectionString": "Host=localhost;Port=5432;Database=curio;Username=orleans;Password=**********", "UseJsonFormat": true}, "Streaming": {"Provider": "Kafka", "ConnectionString": "localhost:9092"}, "Reminders": {"Provider": "AdoNet", "ConnectionString": "Host=localhost;Port=5432;Database=curio;Username=orleans;Password=**********"}}, "Kafka": {"BrokerList": ["localhost:9092"], "ConsumerGroupId": "orleans-event-streams-dev", "Topics": ["domain-events", "verification-events", "user-events"], "SessionTimeoutMs": 30000, "HeartbeatIntervalMs": 3000, "AutoOffsetReset": "earliest", "EnableAutoCommit": true, "AutoCommitIntervalMs": 5000, "MaxPollRecords": 500, "FetchMinBytes": 1, "FetchMaxWaitMs": 500, "SecurityProtocol": "SASL_PLAINTEXT", "SaslMechanism": "PLAIN", "SaslUsername": "admin", "SaslPassword": "admin-secret"}, "Email": {"Smtp": {"Host": "smtp.gmail.com", "Port": 587, "Username": "<EMAIL>", "Password": "your-app-password", "EnableSsl": true, "TimeoutSeconds": 30, "UseDefaultCredentials": false}, "Templates": {"TemplatesDirectory": "EmailTemplates", "EnableCaching": true, "CacheExpirationMinutes": 5}, "DefaultSender": {"FromEmail": "<EMAIL>", "FromName": "Curio Development", "ReplyToEmail": "<EMAIL>", "ReplyToName": "Curio Support"}, "Retry": {"MaxAttempts": 3, "DelayMilliseconds": 1000, "ExponentialBackoff": true}}}