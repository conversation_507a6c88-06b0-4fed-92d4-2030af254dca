using Microsoft.AspNetCore.Mvc;
using Curio.Application.Interfaces;
using Curio.Shared.Users;

namespace Curio.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class UsersController : ControllerBase
{
    private readonly IUserService _userService;
    private readonly ILogger<UsersController> _logger;

    public UsersController(IUserService userService, ILogger<UsersController> logger)
    {
        _userService = userService;
        _logger = logger;
    }

    [HttpPost("check-email")]
    public async Task<ActionResult<EmailExistsResult>> CheckEmailExists([FromBody] CheckEmailExistsCommand command)
    {
        try
        {
            var result = await _userService.CheckEmailExistsAsync(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking email existence for {Email}", command.Email);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("send-verification-code")]
    public async Task<ActionResult<bool>> SendVerificationCode([FromBody] SendVerificationCodeCommand command)
    {
        try
        {
            var result = await _userService.SendVerificationCodeAsync(command);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending verification code to {Email}", command.Email);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("register")]
    public async Task<ActionResult<VerificationResult>> Register([FromBody] RegisterUserCommand command)
    {
        try
        {
            var result = await _userService.RegisterUserAsync(command);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering user {Email}", command.Email);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("login")]
    public async Task<ActionResult<VerificationResult>> Login([FromBody] LoginUserCommand command)
    {
        try
        {
            var result = await _userService.LoginUserAsync(command);
            
            if (result.Success)
            {
                return Ok(result);
            }
            
            return BadRequest(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging in user {Email}", command.Email);
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpGet("{email}")]
    public async Task<ActionResult<UserDto>> GetUser(string email)
    {
        try
        {
            var user = await _userService.GetUserAsync(email);
            
            if (user == null)
            {
                return NotFound();
            }
            
            return Ok(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user {Email}", email);
            return StatusCode(500, "Internal server error");
        }
    }
}