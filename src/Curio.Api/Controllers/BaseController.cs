using Microsoft.AspNetCore.Mvc;

namespace Curio.Api.Controllers;

[ApiController]
public abstract class BaseController : ControllerBase
{
    protected ActionResult<T> HandleResult<T>(T result)
    {
        if (result == null)
        {
            return NotFound();
        }

        return Ok(result);
    }

    protected ActionResult HandleResult(bool success, string? errorMessage = null)
    {
        if (success)
        {
            return Ok();
        }

        return BadRequest(errorMessage ?? "Operation failed");
    }
}